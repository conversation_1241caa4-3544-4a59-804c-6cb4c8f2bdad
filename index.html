<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手艺人修仙指南</title>
    <script src="libs/tailwindcss.js"></script>
    <script src="libs/js-yaml.min.js"></script>
    <script src="libs/marked.min.js"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-gradient-to-br from-slate-50 to-slate-100 min-h-screen">
    <!-- 导航栏 -->
    <nav class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200 shadow-sm">
        <div class="max-w-6xl mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                        手艺人修仙指南
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-lg hover:bg-slate-100 transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- 侧边栏 - 章节目录 -->
            <div class="lg:col-span-1">
                <div class="sticky top-24 bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-slate-200">
                    <h2 class="text-lg font-semibold mb-4 text-slate-800">章节目录</h2>
                    <div id="chapter-list" class="space-y-2">
                        <!-- 章节列表将由 JavaScript 动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="lg:col-span-3">
                <!-- 小说信息卡片 -->
                <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-slate-200 mb-8">
                    <div class="text-center mb-6">
                        <h1 class="text-3xl font-bold mb-2 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                            手艺人修仙指南
                        </h1>
                        <p class="text-slate-600 text-lg">都市修仙 × 传统手艺 × 反套路系统</p>
                    </div>
                    
                    <div class="prose prose-lg max-w-none text-slate-700 mb-6">
                        <p>在现代都市的喧嚣中，主角林昭意外获得了一个名为"匠心传承"的系统。然而，这个系统并非直接赋予强大超能力，而是以一种"反向"机制运作：主角的任何尝试使用超能力都会受到巨大限制或引来不幸，除非他将这些能力与特定的传统手艺结合，并达到精通境界。</p>
                        <p>为了生存和摆脱系统的"惩罚"，林昭被迫踏上了学习各种传统手艺的道路：木工、铁艺、玉雕、苏绣、扎染、古籍修复、药膳烹饪等。在这个过程中，他发现通过"手艺"作为媒介，他的异能不仅不再受限，还能爆发出远超预期的力量。</p>
                    </div>

                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">都市修仙</span>
                        <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">传统手艺</span>
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">反套路系统</span>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">匠人精神</span>
                        <span class="px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm">逆袭成长</span>
                    </div>
                    
                    <div class="text-center">
                        <button id="start-reading" class="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                            开始阅读
                        </button>
                    </div>
                </div>

                <!-- 章节内容区域 -->
                <div id="chapter-content" class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-slate-200 hidden">
                    
                    <!-- 标题和标题复制按钮 -->
                    <div class="mb-6 flex items-center">
                        <h2 id="chapter-title" class="text-2xl font-bold mb-2 text-slate-800"></h2>
                        <button id="copy-chapter-title" class="ml-3 px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition text-sm">
                            复制标题
                        </button>
                    </div>
                    <!-- 章节元信息 -->
                    <div class="flex items-center space-x-4 text-sm text-slate-500">
                        <span id="chapter-author">作者：AI</span>
                        <span id="chapter-date"></span>
                        <span id="chapter-words"></span>
                    </div>
                    <div class="flex justify-between items-center mt-8 pt-6 border-t border-slate-200">
                        <button id="prev-chapter" class="px-6 py-2 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors" disabled>
                            上一章
                        </button>
                        <button id="back-to-index" class="px-6 py-2 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg transition-colors">
                            返回目录
                        </button>
                        <button id="next-chapter" class="px-6 py-2 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors">
                            下一章
                        </button>
                    </div>

                    <!-- 正文复制按钮 -->
                    <div class="flex justify-end mb-4">
                        <button id="copy-chapter-text" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition">
                            复制正文
                        </button>
                    </div>
                    <!-- 正文内容 -->
                    <div id="chapter-text" class="prose prose-lg max-w-none text-slate-700">
                        <!-- 章节内容将在这里显示 -->
                    </div>
                    

                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 shadow-2xl text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-slate-600">加载中...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
