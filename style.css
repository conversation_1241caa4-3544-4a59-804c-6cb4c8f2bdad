/* 自定义样式 */
.prose {
    max-width: none;
}

.prose p {
    margin-bottom: 1.5em;
    line-height: 1.8;
    text-align: justify;
}

.prose h1, .prose h2, .prose h3 {
    margin-top: 2em;
    margin-bottom: 1em;
    font-weight: 600;
}

.prose blockquote {
    border-left: 4px solid #8b5cf6;
    padding-left: 1rem;
    margin: 1.5em 0;
    font-style: italic;
    background: rgba(139, 92, 246, 0.05);
    border-radius: 0 0.5rem 0.5rem 0;
}

/* 章节内容特殊样式 */
.chapter-dialogue {
    color: #4f46e5;
    font-weight: 500;
}

.chapter-system {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.75rem;
    margin: 1.5rem 0;
    font-family: 'Courier New', monospace;
}

.chapter-thought {
    font-style: italic;
    color: #6b7280;
    border-left: 2px solid #d1d5db;
    padding-left: 1rem;
    margin: 1rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grid-cols-1.lg\\:grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    .lg\\:col-span-1, .lg\\:col-span-3 {
        grid-column: span 1;
    }
    
    .sticky {
        position: relative;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}
